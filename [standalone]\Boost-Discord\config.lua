Config = {}

Config.Discord = {
    IsEnabled = true,                           -- Enable/Disable Discord Rich Presence
    ApplicationId = "1399152795969388635",      -- Discord Application ID
    IconLarge = "largeimage",                   -- Large icon asset name
    IconLargeHoverText = "Boost#4383",          -- Large icon hover text
    IconSmall = "smallimage",                   -- Small icon asset name  
    IconSmallHoverText = "Online",              -- Small icon hover text
    UpdateRate = 15000,                         -- Update rate in milliseconds (15 seconds)
    ShowPlayerCount = true,                     -- Show current player count
    MaxPlayers = 200,                           -- Maximum server slots
    
    Buttons = {
        {
            text = "Discord",
            url = "https://discord.gg/heartsofchicago"
        },
        {
            text = "Connect", 
            url = "fivem://connect/cfx.re/join/bpvp3b"
        }
    }
}
