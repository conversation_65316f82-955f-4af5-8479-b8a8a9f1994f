Config = {}

-- Discord Application Settings
Config.AppId = 1399152795969388635   -- Put your discord bot client id here

-- Asset Configuration
Config.LargeAsset = {
    image = "largeimage",  -- Large image asset name from Discord Developer Portal
    text = "Boost#4383"    -- Text shown when hovering over large image
}

Config.SmallAsset = {
    image = "smallimage",  -- Small image asset name from Discord Developer Portal
    text = "Online"        -- Text shown when hovering over small image
}

-- Button Configuration
Config.Buttons = {
    {label = "Discord", url = "https://discord.gg/heartsofchicago"},
    {label = "Connect", url = "fivem://connect/{server_endpoint}"}
}

-- Rich Presence Options
Config.Options = {
    updateInterval = 15000,     -- Update interval in milliseconds (15 seconds)
    showPlayerName = true,      -- Show player name in rich presence
    showStreetName = true,      -- Show current street/location
    showPlayerActivity = true,  -- Show what player is doing (job, vehicle, etc.)
    maxPlayers = 200           -- Maximum server slots
}

-- Legacy support for existing functionality
Config.ClientID = Config.AppId
Config.PlayerCount = Config.Options.maxPlayers
Config.PlayerText = "Players"
Config.ResourceTimer = 5
Config.UseESXIdentity = false
Config.RichPresence = 'ID: %s | %s | %s %s/%s'
Config.UseJobs = false
