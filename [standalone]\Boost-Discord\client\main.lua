-- Rich Presence Variables
local totalPlayers = 0
local maxPlayers = Config.Options.maxPlayers
local playerName = "Unknown"
local queueSize = 0
local n4QueueAvailable = false
local playerLoaded = false
local firstSpawn = true

-- ESX Integration
RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
    ESX.PlayerData = xPlayer
    playerLoaded = true
    playerName = Config.UseESXIdentity and xPlayer.getName() or GetPlayerName(PlayerId())
end)

-- Update player data from server
RegisterNetEvent("richpresence:updatePlayerData", function(count, max, rpName, queueEnabled)
    totalPlayers = count
    maxPlayers = max
    playerName = rpName
    n4QueueAvailable = queueEnabled
end)

-- Queue size monitoring thread
CreateThread(function()
    while true do
        if n4QueueAvailable then
            local ok, result = pcall(function()
                return exports["n4_queue"]:GetQueueSize()
            end)
            if ok then queueSize = result end
        end
        Wait(15000)
    end
end)

-- Player activity detection
local function getPlayerActivity()
    local ped = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(ped, false)

    -- Check if player is in a vehicle
    if vehicle ~= 0 then
        local vehicleClass = GetVehicleClass(vehicle)
        local vehicleModel = GetEntityModel(vehicle)
        local vehicleName = GetDisplayNameFromVehicleModel(vehicleModel)

        -- Determine activity based on vehicle type
        if vehicleClass == 18 then -- Emergency vehicles
            return "on duty (Emergency Services)"
        elseif vehicleClass == 8 then -- Motorcycles
            return "riding a " .. vehicleName
        elseif vehicleClass == 14 then -- Boats
            return "sailing"
        elseif vehicleClass == 15 or vehicleClass == 16 then -- Helicopters/Planes
            return "flying"
        else
            return "driving a " .. vehicleName
        end
    end

    -- Check if player is doing specific activities
    if IsPedShooting(ped) then
        return "in combat"
    elseif IsPedRunning(ped) then
        return "running around"
    elseif IsPedWalking(ped) then
        return "walking around"
    elseif IsPedStill(ped) then
        return "standing idle"
    else
        return "roaming around"
    end
end

-- Get current location
local function getCurrentLocation()
    if not Config.Options.showStreetName then
        return ""
    end

    local coords = GetEntityCoords(PlayerPedId())
    local streetHash = GetStreetNameAtCoord(coords.x, coords.y, coords.z)
    local street = GetStreetNameFromHashKey(streetHash)

    if not street or street == "" then
        local zone = GetLabelText(GetNameOfZone(coords.x, coords.y, coords.z))
        street = zone ~= nil and zone ~= "" and zone or "Secret Location"
    end

    return street
end

-- Initialize Discord Rich Presence
AddEventHandler('playerSpawned', function()
    if firstSpawn then
        -- Set Discord App ID and assets
        SetDiscordAppId(Config.AppId)
        SetDiscordRichPresenceAsset(Config.LargeAsset.image)
        SetDiscordRichPresenceAssetText(Config.LargeAsset.text)
        SetDiscordRichPresenceAssetSmall(Config.SmallAsset.image)
        SetDiscordRichPresenceAssetSmallText(Config.SmallAsset.text)

        -- Set buttons
        for i, btn in ipairs(Config.Buttons or {}) do
            if i == 1 then
                SetDiscordRichPresenceAction(0, btn.label, btn.url)
            elseif i == 2 then
                SetDiscordRichPresenceAction(1, btn.label, btn.url)
            end
        end

        firstSpawn = false
    end
end)

-- Legacy event handler for backward compatibility
RegisterNetEvent('Boost-Discord:SetPresence')
AddEventHandler('Boost-Discord:SetPresence', function(data)
    totalPlayers = data['ActivePlayers']
    if Config.UseESXIdentity then
        playerName = data['IdentityName']
    else
        playerName = data['PlayerName']
    end
end)

-- Main Rich Presence update thread
CreateThread(function()
    while true do
        -- Wait for player to be loaded
        if not playerLoaded then
            Wait(1000)
            goto continue
        end

        -- Build rich presence text
        local line1 = string.format("Online: %d/%d | Queue: %d", totalPlayers, maxPlayers, queueSize)

        local name = Config.Options.showPlayerName and playerName or "Player"
        local activity = Config.Options.showPlayerActivity and getPlayerActivity() or "exploring"
        local location = getCurrentLocation()

        local line2
        if location ~= "" then
            line2 = string.format("%s is %s in %s", name, activity, location)
        else
            line2 = string.format("%s is %s", name, activity)
        end

        SetRichPresence(line1 .. "\n" .. line2)

        ::continue::
        Wait(Config.Options.updateInterval)
    end
end)

-- Legacy thread for backward compatibility
Citizen.CreateThread(function()
    while not playerLoaded do
        Citizen.Wait(10)
    end
    while true do
        TriggerServerEvent('Boost-Discord:UpdatePresence')
        Citizen.Wait(Config.ResourceTimer*1000)
    end
end)

