-- Check if n4_queue resource exists
local function isQueueAvailable()
    return GetResourceState("n4_queue") == "started"
end

-- Update all players with current server data
local function updateAllPlayersData()
    local playerCount = GetNumPlayerIndices()
    local maxPlayers = Config.Options.maxPlayers
    local queueEnabled = isQueueAvailable()

    local players = GetPlayers()
    for _, playerId in ipairs(players) do
        local playerIdNum = tonumber(playerId)
        if playerIdNum then
            local xPlayer = ESX.GetPlayerFromId(playerIdNum)
            if xPlayer then
                local playerName = Config.UseESXIdentity and xPlayer.getName() or GetPlayerName(playerIdNum)
                TriggerClientEvent("richpresence:updatePlayerData", playerIdNum, playerCount, maxPlayers, playerName, queueEnabled)
            end
        end
    end
end

-- Update player data every 30 seconds
CreateThread(function()
    while true do
        updateAllPlayersData()
        Wait(30000) -- 30 seconds
    end
end)

-- Update when player joins
RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function()
    Wait(5000) -- Wait a bit for player to fully load
    updateAllPlayersData()
end)

-- Update when player leaves
AddEventHandler('playerDropped', function()
    Wait(1000) -- Wait a bit for player count to update
    updateAllPlayersData()
end)

-- Legacy event handler for backward compatibility
RegisterServerEvent('Boost-Discord:UpdatePresence')
AddEventHandler('Boost-Discord:UpdatePresence', function()
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if not xPlayer then
        return
    end

    local data = {
        ['PlayerName'] = GetPlayerName(_source),
        ['IdentityName'] = xPlayer.getName(),
        ['Job'] = {jobName = xPlayer.getJob().name, jobGrade = xPlayer.getJob().grade_label, jobLabel = xPlayer.getJob().label},
        ['ActivePlayers'] = GetNumPlayerIndices()
    }

    TriggerClientEvent('Boost-Discord:SetPresence', _source, data)
end)

